<?php

namespace Tests\Feature\BackOffice;

use App\Enums\Trips\TripStatus;
use App\Enums\VehicleTypesCategories;
use App\Filament\Resources\Panel\TripResource;
use App\Models\Area;
use App\Models\Driver;
use App\Models\Rider;
use App\Models\Trip;
use App\Models\TripLocation;
use App\Models\User;
use App\Models\Vehicle;
use App\Models\VehicleType;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class CurrentTripsManagementTest extends TestCase
{
    use RefreshDatabase;

    protected User $admin;

    protected Driver $driver;

    protected Rider $rider;

    protected Vehicle $passengerVehicle;

    protected Vehicle $freightVehicle;

    protected VehicleType $passengerVehicleType;

    protected VehicleType $freightVehicleType;

    protected Area $area;

    protected function setUp(): void
    {
        parent::setUp();

        // Create roles
        Role::create(['name' => 'super_admin']);
        Role::create(['name' => 'admin']);

        // Create permissions
        Permission::create(['name' => 'view_any_panel::trip']);
        Permission::create(['name' => 'view_panel::trip']);
        Permission::create(['name' => 'create_panel::trip']);
        Permission::create(['name' => 'update_panel::trip']);
        Permission::create(['name' => 'delete_panel::trip']);

        // Create admin user
        $this->admin = User::factory()->create([
            'email' => '<EMAIL>',
            'type' => 'admin',
        ]);
        $this->admin->assignRole('super_admin');

        // Give admin user necessary permissions for trip management
        $this->admin->givePermissionTo([
            'view_any_panel::trip',
            'view_panel::trip',
            'create_panel::trip',
            'update_panel::trip',
            'delete_panel::trip',
        ]);

        // Create vehicle types
        $this->passengerVehicleType = VehicleType::factory()->create([
            'name_en' => 'Economy',
            'category' => VehicleTypesCategories::Passenger,
        ]);

        $this->freightVehicleType = VehicleType::factory()->create([
            'name_en' => 'Light Truck',
            'category' => VehicleTypesCategories::Freight,
        ]);

        // Create area
        $this->area = Area::factory()->create([
            'name_en' => 'Tripoli Central',
        ]);

        // Create driver and rider
        $this->driver = Driver::factory()->create();
        $this->rider = Rider::factory()->create();

        // Create vehicles
        $this->passengerVehicle = Vehicle::factory()->create([
            'vehicle_type_id' => $this->passengerVehicleType->id,
            'seat_number' => 4,
            'color' => 'blue',
            'year' => 2022,
        ]);

        $this->freightVehicle = Vehicle::factory()->create([
            'vehicle_type_id' => $this->freightVehicleType->id,
            'year' => 2020,
            'color' => 'white',
        ]);
    }

    /** @test */
    public function admin_can_view_current_trips_list()
    {
        $this->actingAs($this->admin);

        // Create current trips (in progress statuses)
        $currentTrips = collect([
            Trip::factory()->create([
                'driver_id' => $this->driver->id,
                'rider_id' => $this->rider->id,
                'status' => TripStatus::assigned,
                'estimated_arrival_time' => Carbon::now()->addMinutes(30),
                'pricing_breakdown' => json_encode(['total' => 25.50, 'currency' => 'LYD']),
            ]),
            Trip::factory()->create([
                'driver_id' => $this->driver->id,
                'rider_id' => $this->rider->id,
                'status' => TripStatus::on_trip,
                'estimated_arrival_time' => Carbon::now()->addMinutes(15),
                'pricing_breakdown' => json_encode(['total' => 18.75, 'currency' => 'LYD']),
            ]),
            Trip::factory()->create([
                'driver_id' => $this->driver->id,
                'rider_id' => $this->rider->id,
                'status' => TripStatus::pending,
                'estimated_arrival_time' => Carbon::now()->addMinutes(45),
                'pricing_breakdown' => json_encode(['total' => 32.00, 'currency' => 'LYD']),
            ]),
        ]);

        // Create trip locations for each trip
        foreach ($currentTrips as $trip) {
            $tripLocation = TripLocation::factory()->create([
                'trip_id' => $trip->id,
                'departure_address' => 'Pickup Location for Trip '.$trip->id,
                'arrival_address' => 'Dropoff Location for Trip '.$trip->id,
            ]);

            // Update trip to reference the trip location
            $trip->update(['trip_location_id' => $tripLocation->id]);
        }

        // Create completed trip (should not appear in current trips)
        $completedTrip = Trip::factory()->create([
            'driver_id' => $this->driver->id,
            'rider_id' => $this->rider->id,
            'status' => TripStatus::completed,
        ]);

        // Test that admin can access the trips list page
        Livewire::test(TripResource\Pages\ListTrips::class)
            ->assertSuccessful();

        // Verify that current trips are visible in the database
        $this->assertDatabaseHas('trips', [
            'id' => $currentTrips->first()->id,
            'status' => TripStatus::assigned->value,
        ]);

        $this->assertDatabaseHas('trips', [
            'id' => $currentTrips->get(1)->id,
            'status' => TripStatus::on_trip->value,
        ]);

        $this->assertDatabaseHas('trips', [
            'id' => $currentTrips->last()->id,
            'status' => TripStatus::pending->value,
        ]);
    }

    /** @test */
    /** @test */
    public function current_trips_list_shows_required_information()
    {
        $this->actingAs($this->admin);
        app()->setLocale('en'); // Force English locale

        $trip = Trip::factory()->create([
            'driver_id' => $this->driver->id,
            'rider_id' => $this->rider->id,
            'status' => TripStatus::on_trip,
            'estimated_arrival_time' => Carbon::now()->addMinutes(20),
            'pricing_breakdown' => json_encode(['total' => 28.50, 'currency' => 'LYD']),
        ]);

        // Create trip location
        $tripLocation = TripLocation::factory()->create([
            'trip_id' => $trip->id,
            'departure_address' => 'Tripoli Central Market',
            'arrival_address' => 'Tripoli Airport Terminal 1',
        ]);

        // Update trip to reference the trip location
        $trip->update(['trip_location_id' => $tripLocation->id]);

        // Ensure driver and rider have predictable names
        $this->driver->user->update(['name' => 'John Driver']);
        $this->rider->user->update(['name' => 'Jane Rider']);

        Livewire::test(TripResource\Pages\ListTrips::class)
            ->assertSuccessful()
            ->call('$set', 'activeTab', 'current')                       // Switch to current trips tab
            ->assertSeeText($trip->id)                                    // Trip ID
            ->assertSeeText('John Driver')                               // Driver name
            ->assertSeeText('Jane Rider')                                // Rider name
            ->assertSeeText('Tripoli Central')                           // Pickup location (truncated to 15 chars)
            ->assertSeeText('Tripoli Airport')                           // Drop-off location (truncated to 15 chars)
            ->assertSeeText($trip->estimated_arrival_time->format('H:i')) // ETA
            ->assertSeeText('29');                                       // Fare (rounded from 28.50)
    }

    /** @test */
    /** @test */
    public function admin_can_view_detailed_current_trip_information()
    {
        $this->actingAs($this->admin);
        app()->setLocale('en'); // Force English locale

        $trip = Trip::factory()->create([
            'driver_id' => $this->driver->id,
            'rider_id' => $this->rider->id,
            'vehicle_id' => $this->passengerVehicle->id,
            'status' => TripStatus::on_trip,
            'estimated_arrival_time' => Carbon::createFromTime(9, 45), // Fixed time for consistency
            'pricing_breakdown' => json_encode(['total' => 35.75, 'currency' => 'LYD']),
            'rider_notes' => 'Please call when you arrive',
            'is_female' => true,
        ]);

        // Set predictable user data
        $this->driver->user->update([
            'name' => 'John Driver',
            'last_name' => 'Doe',
            'phone_number' => '+218912345678',
            'gender' => 'male',
        ]);
        $this->rider->user->update([
            'name' => 'Jane Rider',
            'last_name' => 'Smith',
            'phone_number' => '+218987654321',
            'gender' => 'female',
        ]);

        // Associate passenger vehicle with driver
        $this->passengerVehicle->drivers()->attach($this->driver);

        // Create trip with location
        $tripLocation = TripLocation::factory()->create([
            'trip_id' => $trip->id,
            'departure_address' => 'Green Square, Tripoli',
            'arrival_address' => 'Tripoli International Airport',
        ]);

        // Update trip to reference the trip location
        $trip->update(['trip_location_id' => $tripLocation->id]);

        Livewire::test(TripResource\Pages\ViewTrip::class, ['record' => $trip->id])
            ->assertSuccessful()
            ->assertSeeText($trip->id)                                           // Trip ID
            ->assertSeeText('Green Square, Tripoli')                            // Pickup location
            ->assertSeeText('Tripoli International Airport')                    // Drop-off location
            ->assertSeeText('9:45 AM')                                          // ETA (fixed time)
            ->assertSeeText('John Driver')                                      // Driver first name
            ->assertSeeText('Doe')                                              // Driver last name
            ->assertSeeText('+218 91-2345678')                                  // Driver phone (formatted)
            ->assertSeeText('male')                                             // Driver gender
            ->assertSeeText('Jane Rider')                                       // Rider first name
            ->assertSeeText('Smith')                                            // Rider last name
            ->assertSeeText('+218 98-7654321')                                  // Rider phone (formatted)
            ->assertSeeText('female')                                           // Rider gender
            ->assertSeeText('أشخاص')                                            // Vehicle category (Arabic for passengers)
            ->assertSeeText($this->passengerVehicle->license_plate_number)      // License plate
            ->assertSeeText($this->passengerVehicleType->name_en)               // Vehicle type
            ->assertSeeText($this->passengerVehicle->seat_number)               // Number of seats
            ->assertSeeText($this->passengerVehicle->year)                      // Year
            ->assertSeeText($this->passengerVehicle->color)                     // Color
            ->assertSeeText('36')                                               // Total fare (rounded from 35.75)
            ->assertSeeText('Yes')                                              // Women-Only Service
            ->assertSeeText('Please call when you arrive');                     // Trip notes
    }

    /** @test */
    /** @test */
    public function admin_can_view_freight_vehicle_details_in_current_trip()
    {
        $this->actingAs($this->admin);
        app()->setLocale('en'); // Force English locale

        $trip = Trip::factory()->create([
            'driver_id' => $this->driver->id,
            'rider_id' => $this->rider->id,
            'vehicle_id' => $this->freightVehicle->id,
            'status' => TripStatus::assigned,
            'pricing_breakdown' => json_encode(['total' => 45.00, 'currency' => 'LYD']),
        ]);

        // Create trip location
        $tripLocation = TripLocation::factory()->create([
            'trip_id' => $trip->id,
            'departure_address' => 'Tripoli Port',
            'arrival_address' => 'Tripoli Warehouse',
        ]);
        $trip->update(['trip_location_id' => $tripLocation->id]);

        // Associate freight vehicle with driver
        $this->freightVehicle->drivers()->attach($this->driver);

        Livewire::test(TripResource\Pages\ViewTrip::class, ['record' => $trip->id])
            ->assertSuccessful()
            ->assertSeeText('بضائع')                                        // Vehicle category (Arabic for freight)
            ->assertSeeText($this->freightVehicle->license_plate_number)    // License plate
            ->assertSeeText($this->freightVehicleType->name_en)             // Vehicle type
            ->assertSeeText($this->freightVehicle->year)                    // Year
            ->assertSeeText($this->freightVehicle->color);                  // Color
    }

    /** @test */
    public function admin_can_filter_current_trips_by_date_range()
    {
        $this->actingAs($this->admin);
        app()->setLocale('en'); // Force English locale

        // Create trips on different dates
        $todayTrip = Trip::factory()->create([
            'driver_id' => $this->driver->id,
            'rider_id' => $this->rider->id,
            'status' => TripStatus::on_trip,
            'created_at' => Carbon::today(),
        ]);

        $yesterdayTrip = Trip::factory()->create([
            'driver_id' => $this->driver->id,
            'rider_id' => $this->rider->id,
            'status' => TripStatus::assigned,
            'created_at' => Carbon::yesterday(),
        ]);

        // Create trip locations
        foreach ([$todayTrip, $yesterdayTrip] as $trip) {
            $tripLocation = TripLocation::factory()->create(['trip_id' => $trip->id]);
            $trip->update(['trip_location_id' => $tripLocation->id]);
        }

        Livewire::test(TripResource\Pages\ListTrips::class)
            ->assertSuccessful()
            ->call('$set', 'activeTab', 'current')                       // Switch to current trips tab
            ->filterTable('created_at', [
                'from' => Carbon::today()->format('Y-m-d'),
                'until' => Carbon::today()->format('Y-m-d'),
            ])
            ->assertSeeText($todayTrip->id)
            ->assertDontSeeText($yesterdayTrip->id);
    }

    /** @test */
    public function admin_can_filter_current_trips_by_status()
    {
        $this->actingAs($this->admin);

        $pendingTrip = Trip::factory()->create([
            'driver_id' => $this->driver->id,
            'rider_id' => $this->rider->id,
            'status' => TripStatus::pending,
        ]);

        $assignedTrip = Trip::factory()->create([
            'driver_id' => $this->driver->id,
            'rider_id' => $this->rider->id,
            'status' => TripStatus::assigned,
        ]);

        $onTripTrip = Trip::factory()->create([
            'driver_id' => $this->driver->id,
            'rider_id' => $this->rider->id,
            'status' => TripStatus::on_trip,
        ]);

        // Filter by pending status only
        Livewire::test(TripResource\Pages\ListTrips::class)
            ->assertSuccessful()
            ->filterTable('status', TripStatus::pending->value)
            ->assertCanSeeTableRecords([$pendingTrip])
            ->assertCanNotSeeTableRecords([$assignedTrip, $onTripTrip]);
    }

    /** @test */
    public function admin_can_filter_current_trips_by_area()
    {
        $this->actingAs($this->admin);

        $tripInArea = Trip::factory()->create([
            'driver_id' => $this->driver->id,
            'rider_id' => $this->rider->id,
            'status' => TripStatus::on_trip,
            'departure_area_id' => $this->area->id,
        ]);

        $tripOutsideArea = Trip::factory()->create([
            'driver_id' => $this->driver->id,
            'rider_id' => $this->rider->id,
            'status' => TripStatus::assigned,
            'departure_area_id' => null,
        ]);

        // Filter by specific area
        Livewire::test(TripResource\Pages\ListTrips::class)
            ->assertSuccessful()
            ->filterTable('departure_area_id', $this->area->id)
            ->assertCanSeeTableRecords([$tripInArea])
            ->assertCanNotSeeTableRecords([$tripOutsideArea]);
    }

    /** @test */
    public function admin_can_search_current_trips()
    {
        $this->actingAs($this->admin);

        $trip = Trip::factory()->create([
            'driver_id' => $this->driver->id,
            'rider_id' => $this->rider->id,
            'status' => TripStatus::on_trip,
        ]);

        $otherTrip = Trip::factory()->create([
            'status' => TripStatus::assigned,
        ]);

        // Create trip locations
        $tripLocation = TripLocation::factory()->create(['trip_id' => $trip->id]);
        $trip->update(['trip_location_id' => $tripLocation->id]);

        $otherTripLocation = TripLocation::factory()->create(['trip_id' => $otherTrip->id]);
        $otherTrip->update(['trip_location_id' => $otherTripLocation->id]);

        // Search by trip ID
        Livewire::test(TripResource\Pages\ListTrips::class)
            ->assertSuccessful()
            ->call('$set', 'activeTab', 'current')                       // Switch to current trips tab
            ->searchTable($trip->id)
            ->assertCanSeeTableRecords([$trip])
            ->assertCanNotSeeTableRecords([$otherTrip]);

        // Search by driver name
        Livewire::test(TripResource\Pages\ListTrips::class)
            ->assertSuccessful()
            ->call('$set', 'activeTab', 'current')                       // Switch to current trips tab
            ->searchTable($this->driver->user->name)
            ->assertCanSeeTableRecords([$trip]);

        // Search by rider name
        Livewire::test(TripResource\Pages\ListTrips::class)
            ->assertSuccessful()
            ->call('$set', 'activeTab', 'current')                       // Switch to current trips tab
            ->searchTable($this->rider->user->name)
            ->assertCanSeeTableRecords([$trip]);
    }

    /** @test */
    public function admin_can_configure_current_trips_per_page()
    {
        $this->actingAs($this->admin);

        // Create multiple current trips
        Trip::factory()->count(25)->create([
            'driver_id' => $this->driver->id,
            'rider_id' => $this->rider->id,
            'status' => TripStatus::on_trip,
        ]);

        $component = Livewire::test(TripResource\Pages\ListTrips::class)
            ->assertSuccessful();

        // Verify pagination options are available
        $this->assertNotNull($component->instance()->getTable());
    }

    /** @test */
    public function current_trips_filters_apply_in_real_time()
    {
        $this->actingAs($this->admin);

        $pendingTrip = Trip::factory()->create([
            'driver_id' => $this->driver->id,
            'rider_id' => $this->rider->id,
            'status' => TripStatus::pending,
        ]);

        $assignedTrip = Trip::factory()->create([
            'driver_id' => $this->driver->id,
            'rider_id' => $this->rider->id,
            'status' => TripStatus::assigned,
        ]);

        // Create trip locations
        $pendingTripLocation = TripLocation::factory()->create(['trip_id' => $pendingTrip->id]);
        $pendingTrip->update(['trip_location_id' => $pendingTripLocation->id]);

        $assignedTripLocation = TripLocation::factory()->create(['trip_id' => $assignedTrip->id]);
        $assignedTrip->update(['trip_location_id' => $assignedTripLocation->id]);

        $component = Livewire::test(TripResource\Pages\ListTrips::class)
            ->assertSuccessful()
            ->call('$set', 'activeTab', 'current')                       // Switch to current trips tab
            ->assertCanSeeTableRecords([$pendingTrip, $assignedTrip]);

        // Apply filter and verify real-time update
        $component->filterTable('status', TripStatus::pending->value)
            ->assertCanSeeTableRecords([$pendingTrip])
            ->assertCanNotSeeTableRecords([$assignedTrip]);
    }
}
