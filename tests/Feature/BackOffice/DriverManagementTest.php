<?php

namespace Tests\Feature\BackOffice;

use App\Enums\Drivers\DriverGlobalStatus;
use App\Filament\Resources\Panel\DriverResource;
use App\Models\Driver;
use App\Models\Trip;
use App\Models\TripRating;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class DriverManagementTest extends TestCase
{
    use RefreshDatabase;

    protected User $admin;
    protected Driver $driver1;
    protected Driver $driver2;
    protected Driver $driver3;

    protected function setUp(): void
    {
        parent::setUp();

        // Create roles
        Role::create(['name' => 'super_admin']);
        Role::create(['name' => 'admin']);

        // Create admin user
        $this->admin = User::factory()->create([
            'email' => '<EMAIL>',
            'type' => 'admin',
        ]);
        $this->admin->assignRole('super_admin');

        // Create drivers with different statuses and registration dates
        $this->driver1 = Driver::factory()->create([
            'global_status' => DriverGlobalStatus::active,
            'created_at' => Carbon::now()->subDays(30),
        ]);

        $this->driver2 = Driver::factory()->create([
            'global_status' => DriverGlobalStatus::blocked,
            'created_at' => Carbon::now()->subDays(15),
        ]);

        $this->driver3 = Driver::factory()->create([
            'global_status' => DriverGlobalStatus::pending,
            'created_at' => Carbon::now()->subDays(5),
        ]);

        // Create some trips and ratings for average rating calculation
        $trip1 = Trip::factory()->create(['driver_id' => $this->driver1->id]);
        $trip2 = Trip::factory()->create(['driver_id' => $this->driver1->id]);
        
        TripRating::factory()->create(['trip_id' => $trip1->id, 'rating' => 5]);
        TripRating::factory()->create(['trip_id' => $trip2->id, 'rating' => 4]);
    }

    /** @test */
    public function admin_can_view_drivers_list()
    {
        $this->actingAs($this->admin);

        Livewire::test(DriverResource\Pages\ListDrivers::class)
            ->assertSuccessful()
            ->assertCanSeeTableRecords([$this->driver1, $this->driver2, $this->driver3]);
    }

    /** @test */
    public function drivers_list_shows_required_information()
    {
        $this->actingAs($this->admin);

        Livewire::test(DriverResource\Pages\ListDrivers::class)
            ->assertSuccessful()
            ->assertSee($this->driver1->id)                                    // ID
            ->assertSee($this->driver1->user->name)                            // Driver name
            ->assertSee($this->driver1->user->last_name)                       // Driver last name
            ->assertSee($this->driver1->user->phone_number)                    // Phone number
            ->assertSee($this->driver1->user->gender->value)                   // Gender
            ->assertSee($this->driver1->created_at->format('Y-m-d'))          // Registration Date
            ->assertSee($this->driver1->global_status->value);                // Status
    }

    /** @test */
    public function drivers_list_shows_cover_picture()
    {
        $this->actingAs($this->admin);

        // Update driver with cover picture
        $this->driver1->user->update([
            'cover_picture' => 'https://example.com/driver1-photo.jpg'
        ]);

        Livewire::test(DriverResource\Pages\ListDrivers::class)
            ->assertSuccessful()
            ->assertSee($this->driver1->user->cover_picture);                  // Cover picture URL
    }

    /** @test */
    public function drivers_list_shows_address_information()
    {
        $this->actingAs($this->admin);

        // Create address for driver
        $address = $this->driver1->user->address()->create([
            'street' => '123 Main Street',
            'city' => 'Tripoli',
            'state' => 'Tripoli District',
            'country' => 'Libya',
            'postal_code' => '12345',
        ]);

        Livewire::test(DriverResource\Pages\ListDrivers::class)
            ->assertSuccessful()
            ->assertSee('123 Main Street')                                     // Street address
            ->assertSee('Tripoli');                                           // City
    }

    /** @test */
    public function drivers_list_shows_average_rating()
    {
        $this->actingAs($this->admin);

        // Driver1 should have average rating of 4.5 (from setUp: ratings 5 and 4)
        $expectedRating = '4.5';

        Livewire::test(DriverResource\Pages\ListDrivers::class)
            ->assertSuccessful()
            ->assertSee($expectedRating);                                      // Average rating
    }

    /** @test */
    public function admin_can_filter_drivers_by_status()
    {
        $this->actingAs($this->admin);

        // Filter by active status
        Livewire::test(DriverResource\Pages\ListDrivers::class)
            ->assertSuccessful()
            ->filterTable('global_status', DriverGlobalStatus::active->value)
            ->assertCanSeeTableRecords([$this->driver1])
            ->assertCanNotSeeTableRecords([$this->driver2, $this->driver3]);

        // Filter by blocked status
        Livewire::test(DriverResource\Pages\ListDrivers::class)
            ->assertSuccessful()
            ->filterTable('global_status', DriverGlobalStatus::blocked->value)
            ->assertCanSeeTableRecords([$this->driver2])
            ->assertCanNotSeeTableRecords([$this->driver1, $this->driver3]);

        // Filter by pending status
        Livewire::test(DriverResource\Pages\ListDrivers::class)
            ->assertSuccessful()
            ->filterTable('global_status', DriverGlobalStatus::pending->value)
            ->assertCanSeeTableRecords([$this->driver3])
            ->assertCanNotSeeTableRecords([$this->driver1, $this->driver2]);
    }

    /** @test */
    public function admin_can_filter_drivers_by_registration_date()
    {
        $this->actingAs($this->admin);

        // Filter by recent registrations (last 10 days)
        $recentDate = Carbon::now()->subDays(10);

        Livewire::test(DriverResource\Pages\ListDrivers::class)
            ->assertSuccessful()
            ->filterTable('created_at', [
                'from' => $recentDate->format('Y-m-d'),
                'until' => Carbon::now()->format('Y-m-d'),
            ])
            ->assertCanSeeTableRecords([$this->driver3])  // Registered 5 days ago
            ->assertCanNotSeeTableRecords([$this->driver1, $this->driver2]); // Registered 30 and 15 days ago
    }

    /** @test */
    public function admin_can_search_drivers()
    {
        $this->actingAs($this->admin);

        // Search by driver name
        Livewire::test(DriverResource\Pages\ListDrivers::class)
            ->assertSuccessful()
            ->searchTable($this->driver1->user->name)
            ->assertCanSeeTableRecords([$this->driver1]);

        // Search by phone number
        Livewire::test(DriverResource\Pages\ListDrivers::class)
            ->assertSuccessful()
            ->searchTable($this->driver2->user->phone_number)
            ->assertCanSeeTableRecords([$this->driver2]);

        // Search by driver ID
        Livewire::test(DriverResource\Pages\ListDrivers::class)
            ->assertSuccessful()
            ->searchTable($this->driver3->id)
            ->assertCanSeeTableRecords([$this->driver3]);
    }

    /** @test */
    public function admin_can_view_detailed_driver_information()
    {
        $this->actingAs($this->admin);

        // Create address for comprehensive view
        $this->driver1->user->address()->create([
            'street' => '456 Driver Street',
            'city' => 'Benghazi',
            'state' => 'Benghazi District',
            'country' => 'Libya',
        ]);

        Livewire::test(DriverResource\Pages\ViewDriver::class, ['record' => $this->driver1->id])
            ->assertSuccessful()
            ->assertSee($this->driver1->id)                                    // ID
            ->assertSee($this->driver1->user->name)                            // First name
            ->assertSee($this->driver1->user->last_name)                       // Last name
            ->assertSee($this->driver1->user->phone_number)                    // Phone number
            ->assertSee($this->driver1->user->gender->value)                   // Gender
            ->assertSee('456 Driver Street')                                   // Address
            ->assertSee('Benghazi')                                           // City
            ->assertSee($this->driver1->created_at->format('Y-m-d'))          // Registration date
            ->assertSee($this->driver1->global_status->value);                // Status
    }

    /** @test */
    public function admin_can_sort_drivers_by_different_criteria()
    {
        $this->actingAs($this->admin);

        $component = Livewire::test(DriverResource\Pages\ListDrivers::class)
            ->assertSuccessful();

        // Test sorting by registration date (newest first)
        $component->sortTable('created_at', 'desc');
        
        $tableRecords = $component->instance()->getTableRecords()->toArray();
        
        // Verify that driver3 (most recent) appears first
        $this->assertEquals($this->driver3->id, $tableRecords[0]['id']);
    }

    /** @test */
    public function admin_can_view_driver_statistics()
    {
        $this->actingAs($this->admin);

        // Create additional trips for statistics
        Trip::factory()->count(5)->create(['driver_id' => $this->driver1->id]);

        Livewire::test(DriverResource\Pages\ViewDriver::class, ['record' => $this->driver1->id])
            ->assertSuccessful();
            // Additional assertions would depend on what statistics are shown
            // e.g., total trips, total earnings, completion rate, etc.
    }

    /** @test */
    public function drivers_list_shows_gender_information()
    {
        $this->actingAs($this->admin);

        // Create drivers with different genders
        $maleDriver = Driver::factory()->create();
        $maleDriver->user->update(['gender' => 'male']);

        $femaleDriver = Driver::factory()->create();
        $femaleDriver->user->update(['gender' => 'female']);

        Livewire::test(DriverResource\Pages\ListDrivers::class)
            ->assertSuccessful()
            ->assertSee('male')
            ->assertSee('female');
    }

    /** @test */
    public function admin_can_export_drivers_list()
    {
        $this->actingAs($this->admin);

        // This test assumes there's an export functionality
        $component = Livewire::test(DriverResource\Pages\ListDrivers::class)
            ->assertSuccessful();

        // Verify that the table has export capabilities
        $this->assertNotNull($component->instance()->getTable());
    }

    /** @test */
    public function drivers_list_pagination_works_correctly()
    {
        $this->actingAs($this->admin);

        // Create many drivers to test pagination
        Driver::factory()->count(50)->create();

        $component = Livewire::test(DriverResource\Pages\ListDrivers::class)
            ->assertSuccessful();

        // Verify pagination is working
        $this->assertNotNull($component->instance()->getTable());
        
        // Test that we can see the first page of results
        $tableRecords = $component->instance()->getTableRecords();
        $this->assertGreaterThan(0, $tableRecords->count());
    }

    /** @test */
    public function admin_can_view_driver_without_ratings()
    {
        $this->actingAs($this->admin);

        // Driver2 and Driver3 don't have ratings from setUp
        Livewire::test(DriverResource\Pages\ViewDriver::class, ['record' => $this->driver2->id])
            ->assertSuccessful()
            ->assertSee($this->driver2->user->name);

        // Should handle drivers with no average rating gracefully
        Livewire::test(DriverResource\Pages\ListDrivers::class)
            ->assertSuccessful()
            ->assertCanSeeTableRecords([$this->driver2, $this->driver3]);
    }
}
